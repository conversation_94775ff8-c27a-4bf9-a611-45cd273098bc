[{"name": "React", "isSystem": true}, {"name": "Vue.js", "isSystem": true}, {"name": "Angular", "isSystem": true}, {"name": "Svelte", "isSystem": true}, {"name": "Next.js", "isSystem": true}, {"name": "Nuxt.js", "isSystem": true}, {"name": "Express.js", "isSystem": true}, {"name": "NestJS", "isSystem": true}, {"name": "Fastify", "isSystem": true}, {"name": "Hapi.js", "isSystem": true}, {"name": "Django", "isSystem": true}, {"name": "Flask", "isSystem": true}, {"name": "FastAPI", "isSystem": true}, {"name": "Tornado", "isSystem": true}, {"name": "Pyramid", "isSystem": true}, {"name": "<PERSON><PERSON>", "isSystem": true}, {"name": "Symfony", "isSystem": true}, {"name": "CodeIgniter", "isSystem": true}, {"name": "CakePHP", "isSystem": true}, {"name": "<PERSON><PERSON>", "isSystem": true}, {"name": "Spring Boot", "isSystem": true}, {"name": "JSF", "isSystem": true}, {"name": "<PERSON><PERSON><PERSON>", "isSystem": true}, {"name": "<PERSON><PERSON><PERSON>", "isSystem": true}, {"name": "ASP.NET Core", "isSystem": true}, {"name": "Blazor", "isSystem": true}, {"name": "NancyFX", "isSystem": true}, {"name": "Ruby on Rails", "isSystem": true}, {"name": "<PERSON>", "isSystem": true}, {"name": "Hanami", "isSystem": true}, {"name": "Gin", "isSystem": true}, {"name": "Echo", "isSystem": true}, {"name": "Beego", "isSystem": true}, {"name": "Fiber", "isSystem": true}, {"name": "Express", "isSystem": true}, {"name": "<PERSON><PERSON>", "isSystem": true}, {"name": "NestJS", "isSystem": true}, {"name": "React Native", "isSystem": true}, {"name": "Flutter", "isSystem": true}, {"name": "SwiftUI", "isSystem": true}, {"name": "Jetpack Compose", "isSystem": true}, {"name": "<PERSON><PERSON>", "isSystem": true}, {"name": "Phoenix", "isSystem": true}, {"name": "Meteor", "isSystem": true}, {"name": "Ember.js", "isSystem": true}, {"name": "Backbone.js", "isSystem": true}, {"name": "Marionette.js", "isSystem": true}, {"name": "Alpine.js", "isSystem": true}, {"name": "<PERSON><PERSON>", "isSystem": true}, {"name": "Stimulus", "isSystem": true}, {"name": "Lit", "isSystem": true}, {"name": "Inferno", "isSystem": true}, {"name": "Feathers.js", "isSystem": true}, {"name": "Sapper", "isSystem": true}, {"name": "Remix", "isSystem": true}, {"name": "RedwoodJS", "isSystem": true}, {"name": "Quasar", "isSystem": true}, {"name": "Capacitor", "isSystem": true}, {"name": "Electron", "isSystem": true}, {"name": "NativeScript", "isSystem": true}, {"name": "Expo", "isSystem": true}, {"name": "Onsen UI", "isSystem": true}, {"name": "<PERSON><PERSON>", "isSystem": true}, {"name": "Web2py", "isSystem": true}, {"name": "CherryPy", "isSystem": true}, {"name": "<PERSON><PERSON>", "isSystem": true}, {"name": "<PERSON>g", "isSystem": true}, {"name": "Falcon", "isSystem": true}, {"name": "Masonite", "isSystem": true}, {"name": "<PERSON><PERSON><PERSON>", "isSystem": true}, {"name": "<PERSON><PERSON>", "isSystem": true}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "isSystem": true}, {"name": "<PERSON>", "isSystem": true}, {"name": "FuelPHP", "isSystem": true}, {"name": "Phalcon", "isSystem": true}, {"name": "<PERSON><PERSON>", "isSystem": true}, {"name": "TypiCMS", "isSystem": true}, {"name": "Modx", "isSystem": true}, {"name": "Nette", "isSystem": true}, {"name": "<PERSON><PERSON><PERSON>", "isSystem": true}, {"name": "SilverStripe", "isSystem": true}, {"name": "OctoberCMS", "isSystem": true}, {"name": "Dropwizard", "isSystem": true}, {"name": "Micronaut", "isSystem": true}, {"name": "JHipster", "isSystem": true}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "isSystem": true}, {"name": "<PERSON><PERSON><PERSON>", "isSystem": true}, {"name": "Play Framework", "isSystem": true}, {"name": "Vert.x", "isSystem": true}, {"name": "Grails", "isSystem": true}, {"name": "Blade", "isSystem": true}, {"name": "Ratpack", "isSystem": true}]
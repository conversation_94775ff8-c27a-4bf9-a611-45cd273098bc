{"name": "west-gate-it-hub-typescript", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "build": "npx tsc", "start": "node dist/index.js", "dev": "nodemon src/index.ts", "dep": "NODE_OPTIONS=--max-old-space-size=4096 nodemon --exec ts-node src/index.ts"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@aws-sdk/client-s3": "^3.556.0", "@azure/storage-blob": "^12.23.0", "@types/backblaze-b2": "^1.5.6", "archiver": "^7.0.1", "backblaze-b2": "^1.7.0", "dotenv": "^16.4.5", "express": "^4.19.2", "fast-csv": "^5.0.2", "fs-extra": "^11.3.0", "jsonwebtoken": "^9.0.2", "mime": "^4.0.4", "mime-types": "^2.1.35", "moment": "^2.30.1", "mongoose": "^8.12.1", "multer": "^1.4.5-lts.1", "nodemailer": "^6.9.13", "socket.io": "^4.7.5", "ts-node": "^10.9.2"}, "devDependencies": {"@types/archiver": "^6.0.3", "@types/bcrypt": "^5.0.2", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/fs-extra": "^11.0.4", "@types/jsonwebtoken": "^9.0.6", "@types/mime-types": "^2.1.4", "@types/morgan": "^1.9.9", "@types/multer": "^1.4.11", "@types/node": "^20.12.7", "bcrypt": "^5.1.1", "cors": "^2.8.5", "morgan": "^1.10.0", "nodemon": "^3.1.0", "typescript": "^5.4.5"}}
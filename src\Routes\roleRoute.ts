import * as express from 'express';
import { createRole, updateRole, getAllRoles, getlistByRole, deleteRole, getCount, roleList, getTechnologies, getAllRolesCombined, createOrGetRole, getAllRolesWithHierarchy, migrateOtherRolesToSubRoles } from '../Controllers/roleController';
import { authorizeRoles } from '../Controllers/Middleware/verifyToken';
import { paginationMiddleware } from '../Controllers/Middleware/pagination';

const roleRoute = express.Router();

roleRoute.post("/add", authorizeRoles(), createRole);         
roleRoute.get("/get-list", authorizeRoles(), getAllRoles);          
roleRoute.patch("/update/:id", authorizeRoles(),updateRole);         
roleRoute.delete("/delete/:id", authorizeRoles(), deleteRole);
roleRoute.get("/candidates/:id", authorizeRoles(), getlistByRole);
roleRoute.get("/candidates-count", authorizeRoles(), getCount);
roleRoute.get("/get-all", authorizeRoles(), roleList);
roleRoute.get("/get-technologies", authorizeRoles(), getTechnologies);

// New unified role management
roleRoute.post("/create-or-get", authorizeRoles(), createOrGetRole);
roleRoute.get("/hierarchy", authorizeRoles(), getAllRolesWithHierarchy);
roleRoute.post("/migrate-other-roles", authorizeRoles('Admin'), migrateOtherRolesToSubRoles);

// Public available roles
roleRoute.get("/public/get-technologies", getTechnologies);
roleRoute.get("/public/get-list", getAllRoles);
roleRoute.get("/public/get-all-roles", getAllRolesCombined);

export default roleRoute;
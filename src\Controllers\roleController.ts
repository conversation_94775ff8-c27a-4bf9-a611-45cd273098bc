import { Request, Response } from "express"
import CandidateCvModel from "../Models/candidateCv"
import RoleModel from "../Models/roleModel"
import mongoose from "mongoose";
const technologies = require('../Util/technologies.json');

export const createRole = async (req: Request, res: Response) => {
    try {
        const { name, otherRole } = req.body;
        if (!name) return res.status(400).json({ message: 'Role name is required', status: false });

        const existingRole = await RoleModel.findOne({ name });
        if (existingRole) return res.status(400).json({ message: 'Role already exists', status: false });

        const newRole = new RoleModel({ name, otherRoles: otherRole || [] });
        await newRole.save();
        res.status(201).json({ message: 'Role created successfully', status: true, data: newRole });
    } catch (err: any) {
        res.status(500).json({ message: err.message, status: false });
    }
};

export const updateRole = async (req: Request, res: Response) => {
    try {
        const { id } = req.params;
        const { name, otherRoles } = req.body;
        if (!name) return res.status(400).json({ message: 'Role name is required', status: false });

        const updatedRole = await RoleModel.findByIdAndUpdate(id, { name, otherRoles: otherRoles || [] }, { new: true });
        if (!updatedRole) return res.status(404).json({ message: 'Role not found', status: false });

        res.status(200).json({ message: 'Role updated successfully', status: true, data: updatedRole });
    } catch (err: any) {
        res.status(500).json({ message: err.message, status: false });
    }
};

export const deleteRole = async (req: Request, res: Response) => {
    try {
        const { id } = req.params;
        await RoleModel.findByIdAndDelete(id);
        res.status(200).json({ message: 'Role deleted successfully', status: true });
    } catch (err: any) {
        res.status(500).json({ message: err.message, status: false });
    }
};

export const getAllRoles = async (req: Request, res: Response) => {
    try {
        const { search, startDate, endDate, supplierId } = req.query;
        // const limit = Number(req.pagination?.limit) || 10;
        // const skip = Number(req.pagination?.skip) || 0;

        const query: any = {};
        if (search) {
            query["$or"] = [
                { name: { $regex: search, $options: "i" } },
                { otherRoles: { $regex: search, $options: "i" } }
            ];
        }

        if (startDate && endDate) {
            const start = new Date(startDate as string);
            const end = new Date(endDate as string);
            end.setHours(23, 59, 59, 999);
            query.createdAt = { $gte: start, $lte: end };
        }

        const result = await RoleModel.aggregate([
            { $match: query },
            {
              $lookup: {
                from: "candidatecvs",
                localField: "_id",
                foreignField: "roleId",
                as: "cvs",
              },
            },
            {
              $addFields: {
                cvs: supplierId ? {
                  $filter: {
                    input: "$cvs",
                    as: "cv",
                    cond: {
                      $and: [
                        { $eq: ["$$cv.supplierId", new mongoose.Types.ObjectId(supplierId as string)] },
                        { $eq: ["$$cv.currentRole", "$_id"] }
                      ]
                    }
                  }
                } : "$cvs"
              }
            },
            {
              $lookup: {
                from: "users",
                localField: "cvs.supplierId",
                foreignField: "_id",
                as: "suppliers",
              },
            },
            {
              $addFields: {
                uniqueSuppliers: { $setUnion: ["$cvs.supplierId", []] },
                activeSuppliers: {
                  $filter: {
                    input: "$suppliers",
                    as: "supplier",
                    cond: { $eq: ["$$supplier.active", true] }
                  }
                }
              }
            },
            {
              $addFields: {
                totalSuppliersCount: { $size: "$uniqueSuppliers" },
                activeSuppliersCount: { $size: "$activeSuppliers" },
                totalCandidatesCount: { $size: "$cvs" },
                activeCandidates: {
                  $filter: {
                    input: "$cvs",
                    as: "candidate",
                    cond: {
                      $and: [
                        { $eq: ["$$candidate.active", true] },
                        {
                          $in: ["$$candidate.supplierId",
                            { $map: { input: "$activeSuppliers", as: "sup", in: "$$sup._id" } }
                          ]
                        }
                      ]
                    }
                  }
                }
              }
            },
            {
              $addFields: {
                activeCandidatesCount: { $size: "$activeCandidates" }
              }
            },
            {
              $match: {
                activeSuppliersCount: { $gt: 0 }
              }
            },
            {
              $facet: {
                roles: [
                  { $sort: { createdAt: -1, _id: -1 } },
                  {
                    $project: {
                      _id: 1,
                      name: 1,
                      otherRoles: 1,
                      createdAt: 1,
                      updatedAt: 1,
                      totalSuppliersCount: 1,
                      activeSuppliersCount: 1,
                      totalCandidatesCount: 1,
                      activeCandidatesCount: 1,
                      executiveTrueCount: {
                        $size: {
                          $filter: {
                            input: "$activeCandidates",
                            as: "candidate",
                            cond: { $eq: ["$$candidate.executive", true] }
                          }
                        }
                      },
                      executiveFalseCount: {
                        $size: {
                          $filter: {
                            input: "$activeCandidates",
                            as: "candidate",
                            cond: { $eq: ["$$candidate.executive", false] }
                          }
                        }
                      }
                    }
                  }
                ],
                total: [
                  { $count: "count" }
                ],
                totalActiveCandidates: [
                  { $unwind: "$activeCandidates" },
                  {
                    $group: {
                      _id: "$activeCandidates._id"
                    }
                  },
                  {
                    $count: "count"
                  }
                ],
                totalExecutiveTrue: [
                  { $unwind: "$activeCandidates" },
                  { $match: { "activeCandidates.executive": true } },
                  {
                    $group: {
                      _id: "$activeCandidates._id"
                    }
                  },
                  {
                    $count: "count"
                  }
                ],
                totalExecutiveFalse: [
                  { $unwind: "$activeCandidates" },
                  { $match: { "activeCandidates.executive": false } },
                  {
                    $group: {
                      _id: "$activeCandidates._id"
                    }
                  },
                  {
                    $count: "count"
                  }
                ]
              }
            },
            {
              $project: {
                roles: 1,
                total: { $arrayElemAt: ["$total.count", 0] },
                totalActiveCandidates: { $arrayElemAt: ["$totalActiveCandidates.count", 0] },
                totalExecutiveTrueCount: { $arrayElemAt: ["$totalExecutiveTrue.count", 0] },
                totalExecutiveFalseCount: { $arrayElemAt: ["$totalExecutiveFalse.count", 0] },
              }
            },
            { $sort: { "roles.createdAt": -1, "roles._id": -1 } },
            // { $skip: skip },
            // { $limit: limit },
          ]);

          const roles = result[0]?.roles || [];
          const total = result[0]?.total || 0;
          const totalActiveCandidates = result[0]?.totalActiveCandidates || 0;
          const totalExecutiveTrueCount = result[0]?.totalExecutiveTrueCount || 0;
          const totalExecutiveFalseCount = result[0]?.totalExecutiveFalseCount || 0;
          
        return res.status(200).json({
            message: "Roles fetched successfully",
            status: true,
            data: {
                roles,
                total,
                totalActiveCandidates,
                totalExecutiveTrueCount,
                totalExecutiveFalseCount
            },
        });
    } catch (err: any) {
        return res.status(500).json({
            message: err.message,
            status: false,
        });
    }
};

export const getlistByRole = async (req: Request, res: Response) => {
    try {
        const { id } = req.params;
        const { startDate, endDate, active, executive } = req.query;

        const matchStage: any = { roleId: { $in: [new mongoose.Types.ObjectId(id)] } };
        const dateFilter: any = { roleId: { $in: [new mongoose.Types.ObjectId(id)] } };

        if (startDate && endDate) {
            const start = new Date(startDate as string);
            const end = new Date(endDate as string);
            end.setHours(23, 59, 59, 999);
            dateFilter["createdAt"] = { $gte: start, $lte: end };
            matchStage.createdAt = { $gte: start, $lte: end };
        }
        const totalCandidates = await CandidateCvModel.countDocuments(matchStage);
        if (active === "true") {
            matchStage["active"] = true;
        } else if (active === "false") {
            matchStage["active"] = false;
        }

        if (executive == 'true') {
            matchStage["executive"] = true;
        } else if (executive == "false") {
            matchStage["executive"] = false;
        }
        
        const activeCandidates = await CandidateCvModel.countDocuments({ ...dateFilter, active: true });
        const inActiveCandidates = await CandidateCvModel.countDocuments({ ...dateFilter, active: false });

        const candidates = await CandidateCvModel.find(matchStage)
            .populate("roleId", ["name", "otherRole"])
            .populate("supplierId", "name")
            .populate("currentRole", "name")
            .sort({ active: -1, createdAt: -1 });

        res.status(200).json({
            message: 'Candidates fetched successfully',
            status: true,
            data: candidates,
            meta_data: {
                totalCandidates,
                activeCandidates,
                inActiveCandidates
            }
        });
    } catch (err: any) {
        res.status(500).json({ message: err.message, status: false });
    }
};

export const getCount = async (req: Request, res: Response) => {
    try {
        const roles = await RoleModel.find();

        const roleCounts = await Promise.all(roles.map(async (role) => {
            const count = await CandidateCvModel.countDocuments({ roleId: { $in: [role._id] } });
            return { name: role.name, id: role._id, candidateCount: count };
        }));

        res.status(200).json({ message: 'Roles fetched successfully', status: true, data: roleCounts });
    } catch (err: any) {
        res.status(500).json({ message: err.message, status: false });
    }
};

export const roleList = async (req: Request, res: Response) => {
    try {
        const { search } = req.query;

        const query: any = {};
        if (search) {
            query["$or"] = [
                { name: { $regex: search, $options: "i" } },
                { otherRoles: { $regex: search, $options: "i" } }
            ];
        }

        const roles = await RoleModel.find(query);

        //const totalRoles = await RoleModel.countDocuments(query);

        return res.status(200).json({
            message: "Roles fetched successfully",
            status: true,
            data: {
                roles,
                // total: totalRoles,
                // page: skip / limit + 1,
                // totalPages: Math.ceil(totalRoles / limit),
            },
        });
    } catch (err: any) {
        return res.status(500).json({
            message: err.message,
            status: false,
        });
    }
};

export const getTechnologies = async (req: any, res: Response) => {
    try {
        const { search } = req.query;

        let filteredData = technologies;

        if (search) {
            const searchLower = search.toLowerCase();
            filteredData = technologies.filter((item: any) =>
                item.toLowerCase().includes(searchLower)
            );
        }

        return res.status(200).json({
            message: "Technologies list fetched successfully",
            status: true,
            data: filteredData
        });
    } catch (err: any) {
        return res.status(500).json({
            message: err.message || "Failed to fetch Technologies",
            status: false,
            data: []
        });
    }
};

// Public API to get all roles and other roles combined in one list
export const getAllRolesCombined = async (req: Request, res: Response) => {
    try {
        const { search } = req.query;

        const roles = await RoleModel.find({}).select('name otherRoles relatedRoles');

        const combinedRoles: string[] = [];

        roles.forEach(role => {
            if (role.name) {
                combinedRoles.push(role.name);
            }

            if (role.otherRoles && role.otherRoles.length > 0) {
                role.otherRoles.forEach(otherRole => {
                    if (otherRole && otherRole.trim()) {
                        combinedRoles.push(otherRole.trim());
                    }
                });
            }

            if (role.relatedRoles && role.relatedRoles.length > 0) {
                role.relatedRoles.forEach(relatedRole => {
                    if (relatedRole && relatedRole.trim()) {
                        combinedRoles.push(relatedRole.trim());
                    }
                });
            }
        });

        const uniqueRoles = [...new Set(combinedRoles)].sort((a, b) =>
            a.toLowerCase().localeCompare(b.toLowerCase())
        );

        let filteredRoles = uniqueRoles;
        if (search && typeof search === 'string') {
            const searchLower = search.toLowerCase();
            filteredRoles = uniqueRoles.filter(role =>
                role.toLowerCase().includes(searchLower)
            );
        }

        return res.status(200).json({
            message: "Roles list fetched successfully",
            status: true,
            data: {
                roles: filteredRoles,
                total: filteredRoles.length,
                totalUnique: uniqueRoles.length
            }
        });
    } catch (err: any) {
        return res.status(500).json({
            message: err.message || "Failed to fetch roles",
            status: false,
            data: null
        });
    }
};

// Helper function to find role by name (checks both main and sub roles)
export const findRoleByName = async (roleName: string) => {
    // First check if it exists as a main role
    let role = await RoleModel.findOne({ name: roleName, isActive: true });

    if (role) {
        return {
            roleId: role._id,
            roleName: role.name,
            type: role.type || 'main',
            parentRoleId: role.parentRoleId
        };
    }

    // If not found as main role, check if it exists in otherRoles of any main role
    const parentRole = await RoleModel.findOne({
        otherRoles: roleName,
        isActive: true
    });

    if (parentRole) {
        // Check if this sub-role already exists as a separate document
        let subRole = await RoleModel.findOne({
            name: roleName,
            type: 'sub',
            parentRoleId: parentRole._id,
            isActive: true
        });

        if (!subRole) {
            // Create sub-role document if it doesn't exist
            subRole = new RoleModel({
                name: roleName,
                type: 'sub',
                parentRoleId: parentRole._id,
                isActive: true
            });
            await subRole.save();
        }

        return {
            roleId: subRole._id,
            roleName: subRole.name,
            type: 'sub',
            parentRoleId: parentRole._id,
            parentRoleName: parentRole.name
        };
    }

    return null;
};

// Create or get role (handles both main and sub roles)
export const createOrGetRole = async (req: Request, res: Response) => {
    try {
        const { name, parentRoleId, type = 'main' } = req.body;

        if (!name) {
            return res.status(400).json({
                message: 'Role name is required',
                status: false
            });
        }

        // Check if role already exists
        const existingRole = await findRoleByName(name);
        if (existingRole) {
            return res.status(200).json({
                message: 'Role already exists',
                status: true,
                data: existingRole
            });
        }

        // Validate parent role if creating sub-role
        if (type === 'sub' && parentRoleId) {
            const parentRole = await RoleModel.findById(parentRoleId);
            if (!parentRole) {
                return res.status(404).json({
                    message: 'Parent role not found',
                    status: false
                });
            }

            // Add to parent's otherRoles if not already present
            if (!parentRole.otherRoles.includes(name)) {
                parentRole.otherRoles.push(name);
                await parentRole.save();
            }
        }

        // Create new role
        const newRole = new RoleModel({
            name,
            type,
            parentRoleId: type === 'sub' ? parentRoleId : null,
            isActive: true
        });

        await newRole.save();

        res.status(201).json({
            message: 'Role created successfully',
            status: true,
            data: {
                roleId: newRole._id,
                roleName: newRole.name,
                type: newRole.type,
                parentRoleId: newRole.parentRoleId
            }
        });
    } catch (err: any) {
        res.status(500).json({ message: err.message, status: false });
    }
};

// Get all roles with hierarchy (main roles with their sub-roles)
export const getAllRolesWithHierarchy = async (req: Request, res: Response) => {
    try {
        // Get all main roles
        const mainRoles = await RoleModel.find({
            type: 'main',
            isActive: true
        }).sort({ name: 1 });

        // Get all sub-roles
        const subRoles = await RoleModel.find({
            type: 'sub',
            isActive: true
        }).populate('parentRoleId', 'name').sort({ name: 1 });

        // Build hierarchy
        const rolesWithHierarchy = mainRoles.map(mainRole => {
            const relatedSubRoles = subRoles.filter(subRole =>
                subRole.parentRoleId &&
                subRole.parentRoleId._id.toString() === mainRole._id.toString()
            );

            return {
                _id: mainRole._id,
                name: mainRole.name,
                type: 'main',
                subRoles: relatedSubRoles.map(subRole => ({
                    _id: subRole._id,
                    name: subRole.name,
                    type: 'sub'
                })),
                // Keep backward compatibility
                otherRoles: mainRole.otherRoles || []
            };
        });

        res.status(200).json({
            message: 'Roles with hierarchy fetched successfully',
            status: true,
            data: rolesWithHierarchy
        });
    } catch (err: any) {
        res.status(500).json({ message: err.message, status: false });
    }
};

// Migration utility: Convert existing otherRoles to separate sub-role documents
export const migrateOtherRolesToSubRoles = async (req: Request, res: Response) => {
    try {
        const mainRoles = await RoleModel.find({
            otherRoles: { $exists: true, $not: { $size: 0 } },
            isActive: true
        });

        let migratedCount = 0;
        const migrationResults = [];

        for (const mainRole of mainRoles) {
            const subRolesCreated = [];

            for (const otherRoleName of mainRole.otherRoles) {
                // Check if sub-role already exists
                const existingSubRole = await RoleModel.findOne({
                    name: otherRoleName,
                    type: 'sub',
                    parentRoleId: mainRole._id
                });

                if (!existingSubRole) {
                    // Create new sub-role document
                    const subRole = new RoleModel({
                        name: otherRoleName,
                        type: 'sub',
                        parentRoleId: mainRole._id,
                        isActive: true
                    });

                    await subRole.save();
                    subRolesCreated.push({
                        name: otherRoleName,
                        id: subRole._id
                    });
                    migratedCount++;
                }
            }

            if (subRolesCreated.length > 0) {
                migrationResults.push({
                    mainRole: mainRole.name,
                    subRolesCreated: subRolesCreated
                });
            }
        }

        res.status(200).json({
            message: 'Migration completed successfully',
            status: true,
            data: {
                totalMigrated: migratedCount,
                migrationResults: migrationResults
            }
        });
    } catch (err: any) {
        res.status(500).json({ message: err.message, status: false });
    }
};